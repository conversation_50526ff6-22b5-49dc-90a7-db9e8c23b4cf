import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { ExternalLink, Calendar, Users, TrendingUp } from "lucide-react"

export default function PortfolioPage() {
  const projects = [
    {
      title: "TechStart E-commerce Platform",
      description: "A comprehensive e-commerce solution with advanced inventory management and analytics.",
      image: "/modern-ecommerce-dashboard.png",
      category: "E-commerce",
      technologies: ["React", "Node.js", "MongoDB", "Stripe"],
      client: "TechStart Inc.",
      year: "2024",
      results: ["300% increase in online sales", "50% reduction in cart abandonment", "99.9% uptime"],
      link: "#",
    },
    {
      title: "HealthCare Mobile App",
      description: "A patient management mobile application with appointment scheduling and telemedicine features.",
      image: "/healthcare-mobile-app.png",
      category: "Mobile App",
      technologies: ["React Native", "Firebase", "Node.js", "WebRTC"],
      client: "MedCare Solutions",
      year: "2024",
      results: ["10,000+ active users", "4.8 app store rating", "40% faster appointment booking"],
      link: "#",
    },
    {
      title: "Financial Dashboard",
      description: "A comprehensive financial analytics dashboard for investment management firms.",
      image: "/financial-analytics-dashboard.png",
      category: "Web Application",
      technologies: ["Next.js", "TypeScript", "D3.js", "PostgreSQL"],
      client: "InvestPro Capital",
      year: "2023",
      results: ["Real-time data processing", "Advanced analytics", "Improved decision making"],
      link: "#",
    },
    {
      title: "Restaurant Chain Website",
      description: "A modern website with online ordering system and location management for a restaurant chain.",
      image: "/restaurant-website-with-online-ordering.png",
      category: "Website",
      technologies: ["WordPress", "WooCommerce", "PHP", "MySQL"],
      client: "Gourmet Bistro",
      year: "2023",
      results: ["200% increase in online orders", "Improved brand presence", "Multi-location management"],
      link: "#",
    },
    {
      title: "EdTech Learning Platform",
      description: "An interactive learning management system with video streaming and progress tracking.",
      image: "/educational-platform-interface.png",
      category: "Web Application",
      technologies: ["Vue.js", "Laravel", "AWS", "Redis"],
      client: "EduLearn Academy",
      year: "2023",
      results: ["5,000+ students enrolled", "95% completion rate", "Interactive learning experience"],
      link: "#",
    },
    {
      title: "Real Estate Portal",
      description: "A comprehensive real estate platform with property listings, virtual tours, and CRM integration.",
      image: "/real-estate-website.png",
      category: "Website",
      technologies: ["React", "Express.js", "MongoDB", "Mapbox"],
      client: "PropertyPro Realty",
      year: "2022",
      results: ["500+ property listings", "Virtual tour integration", "Lead generation system"],
      link: "#",
    },
  ]

  const categories = ["All", "Website", "Web Application", "Mobile App", "E-commerce"]

  const stats = [
    { number: "500+", label: "Projects Completed" },
    { number: "200+", label: "Happy Clients" },
    { number: "98%", label: "Client Satisfaction" },
    { number: "24/7", label: "Support Available" },
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-balance">Our Portfolio</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto text-pretty">
            Explore our successful projects and see how we've helped businesses transform their digital presence and
            achieve remarkable results.
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-primary text-primary-foreground">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-secondary">{stat.number}</div>
                <div className="text-primary-foreground/80 mt-2">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <Button
                key={category}
                variant="outline"
                className="hover:bg-primary hover:text-primary-foreground bg-transparent"
              >
                {category}
              </Button>
            ))}
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {projects.map((project, index) => (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                <div className="relative overflow-hidden">
                  <img
                    src={project.image || "/placeholder.svg"}
                    alt={project.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary">{project.category}</Badge>
                  </div>
                </div>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-xl mb-2">{project.title}</CardTitle>
                      <CardDescription className="text-base">{project.description}</CardDescription>
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={project.link}>
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Technologies */}
                  <div>
                    <h4 className="font-semibold mb-2 text-sm">Technologies Used</h4>
                    <div className="flex flex-wrap gap-1">
                      {project.technologies.map((tech, techIndex) => (
                        <Badge key={techIndex} variant="outline" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Client & Year */}
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4" />
                      <span>{project.client}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4" />
                      <span>{project.year}</span>
                    </div>
                  </div>

                  {/* Results */}
                  <div>
                    <h4 className="font-semibold mb-2 text-sm flex items-center">
                      <TrendingUp className="h-4 w-4 mr-1" />
                      Key Results
                    </h4>
                    <ul className="space-y-1">
                      {project.results.map((result, resultIndex) => (
                        <li key={resultIndex} className="text-xs text-muted-foreground flex items-center">
                          <div className="w-1 h-1 bg-primary rounded-full mr-2 flex-shrink-0"></div>
                          {result}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-balance">Our Project Process</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
              We follow a proven methodology to ensure every project is delivered on time and exceeds expectations.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: "01", title: "Discovery", description: "Understanding your requirements and goals" },
              { step: "02", title: "Planning", description: "Creating detailed project roadmap and timeline" },
              { step: "03", title: "Development", description: "Building your solution with regular updates" },
              { step: "04", title: "Launch", description: "Deploying and supporting your project" },
            ].map((phase, index) => (
              <div key={index} className="text-center space-y-4">
                <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto text-xl font-bold">
                  {phase.step}
                </div>
                <h3 className="text-xl font-semibold">{phase.title}</h3>
                <p className="text-muted-foreground">{phase.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center space-y-8">
          <h2 className="text-3xl md:text-4xl font-bold text-balance">Ready to Start Your Project?</h2>
          <p className="text-xl text-primary-foreground/90 text-pretty">
            Let's create something amazing together. Contact us to discuss your project and get a custom quote.
          </p>
          <Button asChild size="lg" variant="secondary" className="text-lg px-8 py-6">
            <Link href="/contact">Start Your Project</Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
