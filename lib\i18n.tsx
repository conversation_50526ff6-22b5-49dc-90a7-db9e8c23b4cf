"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"

export type Language = "en" | "es" | "fr" | "de" | "zh"

interface LanguageContextType {
  language: Language
  setLanguage: (language: Language) => void
  t: (key: string) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Translation data
const translations = {
  en: {
    // Navigation
    "nav.home": "Home",
    "nav.services": "Services",
    "nav.portfolio": "Portfolio",
    "nav.about": "About",
    "nav.blog": "Blog",
    "nav.contact": "Contact",
    "nav.admin": "Admin",

    // Homepage
    "hero.title": "Transform Your Digital Presence",
    "hero.subtitle":
      "We create innovative digital solutions that drive growth, enhance user experiences, and deliver measurable results for your business.",
    "hero.cta.primary": "Get Started Today",
    "hero.cta.secondary": "View Our Work",

    // Stats
    "stats.projects": "Projects Completed",
    "stats.clients": "Happy Clients",
    "stats.team": "Team Members",
    "stats.experience": "Years Experience",

    // Services
    "services.title": "Our Services",
    "services.subtitle": "Comprehensive digital solutions tailored to meet your business needs and drive success.",
    "services.web.title": "Web Development",
    "services.web.description":
      "Custom websites and web applications built with modern technologies and best practices.",
    "services.mobile.title": "Mobile App Development",
    "services.mobile.description": "Native and cross-platform mobile applications for iOS and Android devices.",
    "services.marketing.title": "Digital Marketing",
    "services.marketing.description":
      "Comprehensive digital marketing strategies to grow your online presence and reach.",
    "services.design.title": "UI/UX Design",
    "services.design.description": "Beautiful and intuitive user interfaces that provide exceptional user experiences.",
    "services.ecommerce.title": "E-commerce Solutions",
    "services.ecommerce.description":
      "Complete e-commerce platforms with payment integration and inventory management.",
    "services.consulting.title": "Consulting Services",
    "services.consulting.description": "Strategic technology consulting to help your business make informed decisions.",

    // Why Choose Us
    "why.title": "Why Choose DigiWave?",
    "why.subtitle": "We combine expertise, innovation, and dedication to deliver exceptional results.",
    "why.fast.title": "Fast Delivery",
    "why.fast.description":
      "We deliver projects on time without compromising on quality, ensuring your business stays ahead.",
    "why.secure.title": "Secure & Reliable",
    "why.secure.description":
      "Security and reliability are at the core of everything we build, protecting your business and users.",
    "why.award.title": "Award-Winning",
    "why.award.description":
      "Our work has been recognized by industry leaders and has won multiple awards for excellence.",

    // Testimonials
    "testimonials.title": "What Our Clients Say",
    "testimonials.subtitle": "Don't just take our word for it. Here's what our satisfied clients have to say.",

    // CTA
    "cta.title": "Ready to Transform Your Business?",
    "cta.subtitle":
      "Let's discuss your project and create something amazing together. Get in touch with our team today.",
    "cta.primary": "Start Your Project",
    "cta.secondary": "View Portfolio",

    // Common
    "common.loading": "Loading...",
    "common.error": "An error occurred",
    "common.success": "Success",
  },
  es: {
    // Navigation
    "nav.home": "Inicio",
    "nav.services": "Servicios",
    "nav.portfolio": "Portafolio",
    "nav.about": "Acerca de",
    "nav.blog": "Blog",
    "nav.contact": "Contacto",
    "nav.admin": "Admin",

    // Homepage
    "hero.title": "Transforma Tu Presencia Digital",
    "hero.subtitle":
      "Creamos soluciones digitales innovadoras que impulsan el crecimiento, mejoran las experiencias de usuario y entregan resultados medibles para tu negocio.",
    "hero.cta.primary": "Comenzar Hoy",
    "hero.cta.secondary": "Ver Nuestro Trabajo",

    // Stats
    "stats.projects": "Proyectos Completados",
    "stats.clients": "Clientes Satisfechos",
    "stats.team": "Miembros del Equipo",
    "stats.experience": "Años de Experiencia",

    // Services
    "services.title": "Nuestros Servicios",
    "services.subtitle":
      "Soluciones digitales integrales adaptadas para satisfacer las necesidades de tu negocio e impulsar el éxito.",
    "services.web.title": "Desarrollo Web",
    "services.web.description":
      "Sitios web personalizados y aplicaciones web construidas con tecnologías modernas y mejores prácticas.",
    "services.mobile.title": "Desarrollo de Apps Móviles",
    "services.mobile.description": "Aplicaciones móviles nativas y multiplataforma para dispositivos iOS y Android.",
    "services.marketing.title": "Marketing Digital",
    "services.marketing.description":
      "Estrategias integrales de marketing digital para hacer crecer tu presencia en línea y alcance.",
    "services.design.title": "Diseño UI/UX",
    "services.design.description":
      "Interfaces de usuario hermosas e intuitivas que brindan experiencias de usuario excepcionales.",
    "services.ecommerce.title": "Soluciones E-commerce",
    "services.ecommerce.description":
      "Plataformas de comercio electrónico completas con integración de pagos y gestión de inventario.",
    "services.consulting.title": "Servicios de Consultoría",
    "services.consulting.description":
      "Consultoría tecnológica estratégica para ayudar a tu negocio a tomar decisiones informadas.",

    // Why Choose Us
    "why.title": "¿Por Qué Elegir DigiWave?",
    "why.subtitle": "Combinamos experiencia, innovación y dedicación para entregar resultados excepcionales.",
    "why.fast.title": "Entrega Rápida",
    "why.fast.description":
      "Entregamos proyectos a tiempo sin comprometer la calidad, asegurando que tu negocio se mantenga adelante.",
    "why.secure.title": "Seguro y Confiable",
    "why.secure.description":
      "La seguridad y confiabilidad están en el núcleo de todo lo que construimos, protegiendo tu negocio y usuarios.",
    "why.award.title": "Galardonado",
    "why.award.description":
      "Nuestro trabajo ha sido reconocido por líderes de la industria y ha ganado múltiples premios por excelencia.",

    // Testimonials
    "testimonials.title": "Lo Que Dicen Nuestros Clientes",
    "testimonials.subtitle":
      "No solo tomes nuestra palabra. Esto es lo que nuestros clientes satisfechos tienen que decir.",

    // CTA
    "cta.title": "¿Listo para Transformar Tu Negocio?",
    "cta.subtitle":
      "Hablemos sobre tu proyecto y creemos algo increíble juntos. Ponte en contacto con nuestro equipo hoy.",
    "cta.primary": "Iniciar Tu Proyecto",
    "cta.secondary": "Ver Portafolio",

    // Common
    "common.loading": "Cargando...",
    "common.error": "Ocurrió un error",
    "common.success": "Éxito",
  },
  fr: {
    // Navigation
    "nav.home": "Accueil",
    "nav.services": "Services",
    "nav.portfolio": "Portfolio",
    "nav.about": "À Propos",
    "nav.blog": "Blog",
    "nav.contact": "Contact",
    "nav.admin": "Admin",

    // Homepage
    "hero.title": "Transformez Votre Présence Numérique",
    "hero.subtitle":
      "Nous créons des solutions numériques innovantes qui stimulent la croissance, améliorent les expériences utilisateur et livrent des résultats mesurables pour votre entreprise.",
    "hero.cta.primary": "Commencer Aujourd'hui",
    "hero.cta.secondary": "Voir Notre Travail",

    // Stats
    "stats.projects": "Projets Terminés",
    "stats.clients": "Clients Satisfaits",
    "stats.team": "Membres de l'Équipe",
    "stats.experience": "Années d'Expérience",

    // Services
    "services.title": "Nos Services",
    "services.subtitle":
      "Solutions numériques complètes adaptées pour répondre aux besoins de votre entreprise et stimuler le succès.",
    "services.web.title": "Développement Web",
    "services.web.description":
      "Sites web personnalisés et applications web construites avec des technologies modernes et les meilleures pratiques.",
    "services.mobile.title": "Développement d'Apps Mobiles",
    "services.mobile.description": "Applications mobiles natives et multiplateformes pour appareils iOS et Android.",
    "services.marketing.title": "Marketing Numérique",
    "services.marketing.description":
      "Stratégies de marketing numérique complètes pour développer votre présence en ligne et votre portée.",
    "services.design.title": "Design UI/UX",
    "services.design.description":
      "Interfaces utilisateur belles et intuitives qui offrent des expériences utilisateur exceptionnelles.",
    "services.ecommerce.title": "Solutions E-commerce",
    "services.ecommerce.description":
      "Plateformes de commerce électronique complètes avec intégration de paiement et gestion d'inventaire.",
    "services.consulting.title": "Services de Conseil",
    "services.consulting.description":
      "Conseil technologique stratégique pour aider votre entreprise à prendre des décisions éclairées.",

    // Why Choose Us
    "why.title": "Pourquoi Choisir DigiWave?",
    "why.subtitle": "Nous combinons expertise, innovation et dévouement pour livrer des résultats exceptionnels.",
    "why.fast.title": "Livraison Rapide",
    "why.fast.description":
      "Nous livrons les projets à temps sans compromettre la qualité, assurant que votre entreprise reste en avance.",
    "why.secure.title": "Sécurisé et Fiable",
    "why.secure.description":
      "La sécurité et la fiabilité sont au cœur de tout ce que nous construisons, protégeant votre entreprise et vos utilisateurs.",
    "why.award.title": "Primé",
    "why.award.description":
      "Notre travail a été reconnu par les leaders de l'industrie et a remporté plusieurs prix d'excellence.",

    // Testimonials
    "testimonials.title": "Ce Que Disent Nos Clients",
    "testimonials.subtitle": "Ne nous croyez pas sur parole. Voici ce que nos clients satisfaits ont à dire.",

    // CTA
    "cta.title": "Prêt à Transformer Votre Entreprise?",
    "cta.subtitle":
      "Discutons de votre projet et créons quelque chose d'incroyable ensemble. Contactez notre équipe aujourd'hui.",
    "cta.primary": "Démarrer Votre Projet",
    "cta.secondary": "Voir Portfolio",

    // Common
    "common.loading": "Chargement...",
    "common.error": "Une erreur s'est produite",
    "common.success": "Succès",
  },
  de: {
    // Navigation
    "nav.home": "Startseite",
    "nav.services": "Dienstleistungen",
    "nav.portfolio": "Portfolio",
    "nav.about": "Über Uns",
    "nav.blog": "Blog",
    "nav.contact": "Kontakt",
    "nav.admin": "Admin",

    // Homepage
    "hero.title": "Transformieren Sie Ihre Digitale Präsenz",
    "hero.subtitle":
      "Wir schaffen innovative digitale Lösungen, die Wachstum fördern, Benutzererfahrungen verbessern und messbare Ergebnisse für Ihr Unternehmen liefern.",
    "hero.cta.primary": "Heute Beginnen",
    "hero.cta.secondary": "Unsere Arbeit Ansehen",

    // Stats
    "stats.projects": "Abgeschlossene Projekte",
    "stats.clients": "Zufriedene Kunden",
    "stats.team": "Teammitglieder",
    "stats.experience": "Jahre Erfahrung",

    // Services
    "services.title": "Unsere Dienstleistungen",
    "services.subtitle":
      "Umfassende digitale Lösungen, die auf die Bedürfnisse Ihres Unternehmens zugeschnitten sind und Erfolg fördern.",
    "services.web.title": "Webentwicklung",
    "services.web.description":
      "Maßgeschneiderte Websites und Webanwendungen, die mit modernen Technologien und bewährten Praktiken erstellt wurden.",
    "services.mobile.title": "Mobile App-Entwicklung",
    "services.mobile.description": "Native und plattformübergreifende mobile Anwendungen für iOS- und Android-Geräte.",
    "services.marketing.title": "Digitales Marketing",
    "services.marketing.description":
      "Umfassende digitale Marketingstrategien zur Steigerung Ihrer Online-Präsenz und Reichweite.",
    "services.design.title": "UI/UX Design",
    "services.design.description":
      "Schöne und intuitive Benutzeroberflächen, die außergewöhnliche Benutzererfahrungen bieten.",
    "services.ecommerce.title": "E-Commerce-Lösungen",
    "services.ecommerce.description":
      "Vollständige E-Commerce-Plattformen mit Zahlungsintegration und Bestandsverwaltung.",
    "services.consulting.title": "Beratungsdienstleistungen",
    "services.consulting.description":
      "Strategische Technologieberatung, um Ihrem Unternehmen bei fundierten Entscheidungen zu helfen.",

    // Why Choose Us
    "why.title": "Warum DigiWave Wählen?",
    "why.subtitle": "Wir kombinieren Expertise, Innovation und Hingabe, um außergewöhnliche Ergebnisse zu liefern.",
    "why.fast.title": "Schnelle Lieferung",
    "why.fast.description":
      "Wir liefern Projekte pünktlich ohne Qualitätskompromisse und sorgen dafür, dass Ihr Unternehmen vorne bleibt.",
    "why.secure.title": "Sicher und Zuverlässig",
    "why.secure.description":
      "Sicherheit und Zuverlässigkeit stehen im Mittelpunkt von allem, was wir bauen, und schützen Ihr Unternehmen und Ihre Benutzer.",
    "why.award.title": "Preisgekrönt",
    "why.award.description":
      "Unsere Arbeit wurde von Branchenführern anerkannt und hat mehrere Auszeichnungen für Exzellenz gewonnen.",

    // Testimonials
    "testimonials.title": "Was Unsere Kunden Sagen",
    "testimonials.subtitle": "Glauben Sie nicht nur uns. Hier ist, was unsere zufriedenen Kunden zu sagen haben.",

    // CTA
    "cta.title": "Bereit, Ihr Unternehmen zu Transformieren?",
    "cta.subtitle":
      "Lassen Sie uns über Ihr Projekt sprechen und gemeinsam etwas Erstaunliches schaffen. Kontaktieren Sie unser Team heute.",
    "cta.primary": "Ihr Projekt Starten",
    "cta.secondary": "Portfolio Ansehen",

    // Common
    "common.loading": "Laden...",
    "common.error": "Ein Fehler ist aufgetreten",
    "common.success": "Erfolg",
  },
  zh: {
    // Navigation
    "nav.home": "首页",
    "nav.services": "服务",
    "nav.portfolio": "作品集",
    "nav.about": "关于我们",
    "nav.blog": "博客",
    "nav.contact": "联系我们",
    "nav.admin": "管理员",

    // Homepage
    "hero.title": "转变您的数字化形象",
    "hero.subtitle": "我们创造创新的数字解决方案，推动增长，提升用户体验，为您的业务带来可衡量的结果。",
    "hero.cta.primary": "立即开始",
    "hero.cta.secondary": "查看我们的作品",

    // Stats
    "stats.projects": "已完成项目",
    "stats.clients": "满意客户",
    "stats.team": "团队成员",
    "stats.experience": "年经验",

    // Services
    "services.title": "我们的服务",
    "services.subtitle": "量身定制的综合数字解决方案，满足您的业务需求并推动成功。",
    "services.web.title": "网站开发",
    "services.web.description": "使用现代技术和最佳实践构建的定制网站和网络应用程序。",
    "services.mobile.title": "移动应用开发",
    "services.mobile.description": "适用于iOS和Android设备的原生和跨平台移动应用程序。",
    "services.marketing.title": "数字营销",
    "services.marketing.description": "全面的数字营销策略，扩大您的在线影响力和覆盖范围。",
    "services.design.title": "UI/UX设计",
    "services.design.description": "美观直观的用户界面，提供卓越的用户体验。",
    "services.ecommerce.title": "电商解决方案",
    "services.ecommerce.description": "完整的电子商务平台，集成支付和库存管理。",
    "services.consulting.title": "咨询服务",
    "services.consulting.description": "战略技术咨询，帮助您的企业做出明智决策。",

    // Why Choose Us
    "why.title": "为什么选择DigiWave？",
    "why.subtitle": "我们结合专业知识、创新和奉献精神，提供卓越的结果。",
    "why.fast.title": "快速交付",
    "why.fast.description": "我们按时交付项目，不妥协质量，确保您的业务保持领先。",
    "why.secure.title": "安全可靠",
    "why.secure.description": "安全性和可靠性是我们构建一切的核心，保护您的业务和用户。",
    "why.award.title": "获奖作品",
    "why.award.description": "我们的工作得到了行业领导者的认可，并获得了多项卓越奖项。",

    // Testimonials
    "testimonials.title": "客户评价",
    "testimonials.subtitle": "不要只听我们的话。这是我们满意的客户所说的。",

    // CTA
    "cta.title": "准备好转变您的业务了吗？",
    "cta.subtitle": "让我们讨论您的项目，共同创造令人惊叹的成果。今天就联系我们的团队。",
    "cta.primary": "开始您的项目",
    "cta.secondary": "查看作品集",

    // Common
    "common.loading": "加载中...",
    "common.error": "发生错误",
    "common.success": "成功",
  },
}

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState<Language>("en")

  useEffect(() => {
    // Get saved language from localStorage
    const savedLanguage = localStorage.getItem("digiwave-language") as Language
    if (savedLanguage && translations[savedLanguage]) {
      setLanguage(savedLanguage)
    } else {
      // Detect browser language
      const browserLang = navigator.language.split("-")[0] as Language
      if (translations[browserLang]) {
        setLanguage(browserLang)
      }
    }
  }, [])

  useEffect(() => {
    // Save to localStorage
    localStorage.setItem("digiwave-language", language)

    // Update document language
    document.documentElement.lang = language
  }, [language])

  const t = (key: string): string => {
    return translations[language]?.[key] || translations.en[key] || key
  }

  return <LanguageContext.Provider value={{ language, setLanguage, t }}>{children}</LanguageContext.Provider>
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider")
  }
  return context
}

// Language options for the selector
export const languageOptions = [
  { code: "en" as Language, name: "English", flag: "🇺🇸" },
  { code: "es" as Language, name: "Español", flag: "🇪🇸" },
  { code: "fr" as Language, name: "Français", flag: "🇫🇷" },
  { code: "de" as Language, name: "Deutsch", flag: "🇩🇪" },
  { code: "zh" as Language, name: "中文", flag: "🇨🇳" },
]
