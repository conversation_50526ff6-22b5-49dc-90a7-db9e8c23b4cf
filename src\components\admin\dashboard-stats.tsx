"use client"

import type React from "react"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { TrendingUp, TrendingDown, Users, FileText, Eye, MessageSquare, DollarSign, ShoppingCart } from "lucide-react"
import { useEffect, useState } from "react"

interface StatCardProps {
  title: string
  value: string
  change: string
  trend: "up" | "down"
  icon: React.ElementType
  delay?: number
}

function StatCard({ title, value, change, trend, icon: Icon, delay = 0 }: StatCardProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return (
    <Card
      className={`hover:shadow-lg transition-all duration-500 hover:-translate-y-1 group ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-4"}`}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
          <Icon className="h-4 w-4 text-primary" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-1">{value}</div>
        <div className="flex items-center space-x-1">
          {trend === "up" ? (
            <TrendingUp className="h-4 w-4 text-green-600" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-600" />
          )}
          <span className={`text-xs font-medium ${trend === "up" ? "text-green-600" : "text-red-600"}`}>{change}</span>
          <span className="text-xs text-muted-foreground">from last month</span>
        </div>
      </CardContent>
    </Card>
  )
}

export function DashboardStats() {
  const stats = [
    {
      title: "Total Users",
      value: "1,234",
      change: "+12%",
      trend: "up" as const,
      icon: Users,
    },
    {
      title: "Blog Posts",
      value: "45",
      change: "+3",
      trend: "up" as const,
      icon: FileText,
    },
    {
      title: "Page Views",
      value: "23,456",
      change: "+18%",
      trend: "up" as const,
      icon: Eye,
    },
    {
      title: "Contact Forms",
      value: "89",
      change: "+7",
      trend: "up" as const,
      icon: MessageSquare,
    },
    {
      title: "Revenue",
      value: "$12,345",
      change: "+25%",
      trend: "up" as const,
      icon: DollarSign,
    },
    {
      title: "Orders",
      value: "156",
      change: "-2%",
      trend: "down" as const,
      icon: ShoppingCart,
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} delay={index * 100} />
      ))}
    </div>
  )
}
