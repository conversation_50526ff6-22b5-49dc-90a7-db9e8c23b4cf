"use client"

import { AdminGuard } from "@/src/components/auth/protected-route"
import { useAuth } from "@/src/contexts/auth-context"
import { <PERSON>ton } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardStats } from "@/src/components/admin/dashboard-stats"
import { AnalyticsChart } from "@/src/components/admin/analytics-chart"
import { DataTable } from "@/src/components/admin/data-table"
import Link from "next/link"
import { Bell, LogOut, Settings, Plus, Users, BarChart3, Eye } from "lucide-react"

function AdminDashboardContent() {
  const { user, logout } = useAuth()

  const quickActions = [
    { title: "New Blog Post", icon: Plus, href: "/admin/posts/new", color: "bg-primary hover:bg-primary/90" },
    { title: "Manage Users", icon: Users, href: "/admin/users", color: "bg-secondary hover:bg-secondary/90" },
    { title: "View Analytics", icon: BarChart3, href: "/admin/analytics", color: "bg-accent hover:bg-accent/90" },
    { title: "Settings", icon: Settings, href: "/admin/settings", color: "bg-muted hover:bg-muted/90" },
  ]

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-background via-muted/20 to-background">
      <header className="bg-background/95 backdrop-blur-sm border-b border-border/50 px-6 py-4 sticky top-0 z-50">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Admin Dashboard
            </h1>
            <p className="text-muted-foreground">Welcome back, {user?.name}! Here's your site overview.</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" size="sm" className="hover:scale-105 transition-transform bg-transparent">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
              <span className="ml-2 bg-primary text-primary-foreground text-xs rounded-full px-2 py-0.5">3</span>
            </Button>
            <Button asChild variant="outline" className="hover:scale-105 transition-transform bg-transparent">
              <Link href="/">
                <Eye className="h-4 w-4 mr-2" />
                View Site
              </Link>
            </Button>
            <Button variant="destructive" size="sm" onClick={logout} className="hover:scale-105 transition-transform">
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </header>

      <div className="flex-1 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          <DashboardStats />

          <Card className="hover:shadow-lg transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-primary" />
                Quick Actions
              </CardTitle>
              <CardDescription>Common tasks and shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    asChild
                    variant="outline"
                    className="h-24 flex-col space-y-3 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group bg-transparent"
                  >
                    <Link href={action.href}>
                      <div
                        className={`w-10 h-10 rounded-lg flex items-center justify-center ${action.color} group-hover:scale-110 transition-transform`}
                      >
                        <action.icon className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-sm font-medium">{action.title}</span>
                    </Link>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-300">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-primary" />
                Analytics Overview
              </CardTitle>
              <CardDescription>Comprehensive site analytics and performance metrics</CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsChart />
            </CardContent>
          </Card>

          <DataTable />
        </div>
      </div>
    </div>
  )
}

export default function AdminDashboard() {
  return (
    <AdminGuard>
      <AdminDashboardContent />
    </AdminGuard>
  )
}
