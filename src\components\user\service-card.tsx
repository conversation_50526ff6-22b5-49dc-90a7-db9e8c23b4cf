"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { CheckCircle, type LucideIcon } from "lucide-react"
import Image from "next/image"

interface ServiceCardProps {
  icon: LucideIcon
  title: string
  description: string
  features: string[]
  image?: string
}

export function ServiceCard({ icon: Icon, title, description, features, image }: ServiceCardProps) {
  return (
    <Card className="group hover:shadow-xl transition-all duration-500 border-2 hover:border-primary/20 hover:-translate-y-2 overflow-hidden">
      <div className="relative h-48 overflow-hidden">
        <Image
          src={image || "/placeholder.svg"}
          alt={title}
          fill
          className="object-cover group-hover:scale-110 transition-transform duration-500"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
        <div className="absolute bottom-4 left-4">
          <div className="w-12 h-12 bg-primary/90 rounded-lg flex items-center justify-center group-hover:bg-primary transition-colors">
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
      </div>
      <CardHeader>
        <CardTitle className="text-xl group-hover:text-primary transition-colors">{title}</CardTitle>
        <CardDescription className="text-base">{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2">
          {features.map((feature, index) => (
            <li
              key={index}
              className="flex items-center space-x-2 group-hover:translate-x-1 transition-transform duration-300"
              style={{ transitionDelay: `${index * 0.1}s` }}
            >
              <CheckCircle className="h-4 w-4 text-primary" />
              <span className="text-sm text-muted-foreground">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}
