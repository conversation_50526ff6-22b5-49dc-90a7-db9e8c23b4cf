import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import Link from "next/link"
import { Search, Calendar, ArrowRight, Clock, Tag } from "lucide-react"

export default function BlogPage() {
  const blogPosts = [
    {
      id: 1,
      title: "The Future of Web Development: Trends to Watch in 2024",
      excerpt:
        "Explore the latest trends shaping web development, from AI integration to progressive web apps and the evolution of JavaScript frameworks.",
      image: "/web-development-trends-2024.png",
      author: "<PERSON>",
      authorImage: "/professional-cto-headshot.png",
      date: "2024-01-15",
      readTime: "8 min read",
      category: "Web Development",
      tags: ["JavaScript", "AI", "PWA", "Trends"],
      featured: true,
    },
    {
      id: 2,
      title: "Mobile-First Design: Why It Matters More Than Ever",
      excerpt:
        "Learn why mobile-first design is crucial for modern businesses and how to implement responsive design strategies that convert.",
      image: "/mobile-first-design-strategy.png",
      author: "<PERSON>",
      authorImage: "/professional-designer-headshot.png",
      date: "2024-01-10",
      readTime: "6 min read",
      category: "Design",
      tags: ["Mobile", "UX", "Responsive", "Design"],
      featured: false,
    },
    {
      id: 3,
      title: "Digital Marketing ROI: Measuring Success in 2024",
      excerpt:
        "Discover the key metrics and strategies for measuring digital marketing ROI and optimizing your campaigns for maximum impact.",
      image: "/digital-marketing-analytics.png",
      author: "Emily Davis",
      authorImage: "/professional-man-marketing.png",
      date: "2024-01-05",
      readTime: "10 min read",
      category: "Marketing",
      tags: ["ROI", "Analytics", "Strategy", "Metrics"],
      featured: false,
    },
    {
      id: 4,
      title: "E-commerce Security: Protecting Your Online Store",
      excerpt:
        "Essential security measures every e-commerce business should implement to protect customer data and build trust.",
      image: "/ecommerce-security-guide.png",
      author: "David Kim",
      authorImage: "/professional-developer-headshot.png",
      date: "2023-12-28",
      readTime: "7 min read",
      category: "E-commerce",
      tags: ["Security", "E-commerce", "Privacy", "Trust"],
      featured: false,
    },
    {
      id: 5,
      title: "AI in Business: Practical Applications for SMEs",
      excerpt:
        "How small and medium enterprises can leverage artificial intelligence to streamline operations and improve customer experience.",
      image: "/ai-business-applications.png",
      author: "Alex Johnson",
      authorImage: "/professional-ceo-headshot.png",
      date: "2023-12-20",
      readTime: "9 min read",
      category: "Technology",
      tags: ["AI", "Business", "SME", "Automation"],
      featured: false,
    },
    {
      id: 6,
      title: "Building High-Performance React Applications",
      excerpt:
        "Best practices and optimization techniques for creating fast, scalable React applications that deliver exceptional user experiences.",
      image: "/react-performance-optimization.png",
      author: "Sarah Chen",
      authorImage: "/professional-cto-headshot.png",
      date: "2023-12-15",
      readTime: "12 min read",
      category: "Development",
      tags: ["React", "Performance", "Optimization", "JavaScript"],
      featured: false,
    },
  ]

  const categories = ["All", "Web Development", "Design", "Marketing", "E-commerce", "Technology", "Development"]

  const featuredPost = blogPosts.find((post) => post.featured)
  const regularPosts = blogPosts.filter((post) => !post.featured)

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-balance">DigiWave Blog</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto text-pretty">
            Insights, trends, and expert advice on digital transformation, web development, design, and marketing.
          </p>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-12 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row gap-6 items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input placeholder="Search articles..." className="pl-10" />
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant="outline"
                  size="sm"
                  className="hover:bg-primary hover:text-primary-foreground bg-transparent"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Post */}
      {featuredPost && (
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-2">Featured Article</h2>
              <div className="w-12 h-1 bg-primary"></div>
            </div>
            <Card className="overflow-hidden hover:shadow-xl transition-shadow">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                <div className="relative h-64 lg:h-auto">
                  <img
                    src={featuredPost.image || "/placeholder.svg"}
                    alt={featuredPost.title}
                    className="w-full h-full object-cover"
                  />
                  <Badge className="absolute top-4 left-4 bg-secondary text-secondary-foreground">
                    {featuredPost.category}
                  </Badge>
                </div>
                <div className="p-8 flex flex-col justify-center">
                  <div className="space-y-4">
                    <h3 className="text-2xl md:text-3xl font-bold text-balance">{featuredPost.title}</h3>
                    <p className="text-muted-foreground text-lg leading-relaxed">{featuredPost.excerpt}</p>
                    <div className="flex items-center space-x-6 text-sm text-muted-foreground">
                      <div className="flex items-center space-x-2">
                        <img
                          src={featuredPost.authorImage || "/placeholder.svg"}
                          alt={featuredPost.author}
                          className="w-6 h-6 rounded-full"
                        />
                        <span>{featuredPost.author}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(featuredPost.date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{featuredPost.readTime}</span>
                      </div>
                    </div>
                    <Button asChild size="lg">
                      <Link href={`/blog/${featuredPost.id}`}>
                        Read Full Article
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </section>
      )}

      {/* Blog Posts Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-2">Latest Articles</h2>
            <div className="w-12 h-1 bg-primary"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post) => (
              <Card key={post.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                <div className="relative overflow-hidden">
                  <img
                    src={post.image || "/placeholder.svg"}
                    alt={post.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <Badge className="absolute top-4 left-4 bg-secondary text-secondary-foreground">
                    {post.category}
                  </Badge>
                </div>
                <CardHeader>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors line-clamp-2">
                    <Link href={`/blog/${post.id}`}>{post.title}</Link>
                  </CardTitle>
                  <CardDescription className="line-clamp-3">{post.excerpt}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Tags */}
                  <div className="flex flex-wrap gap-1">
                    {post.tags.slice(0, 3).map((tag, tagIndex) => (
                      <Badge key={tagIndex} variant="outline" className="text-xs">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  {/* Author and Meta */}
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-2">
                      <img
                        src={post.authorImage || "/placeholder.svg"}
                        alt={post.author}
                        className="w-8 h-8 rounded-full"
                      />
                      <div>
                        <p className="text-sm font-medium">{post.author}</p>
                        <p className="text-xs text-muted-foreground">{post.readTime}</p>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">{new Date(post.date).toLocaleDateString()}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center space-y-8">
          <h2 className="text-3xl md:text-4xl font-bold text-balance">Stay Updated</h2>
          <p className="text-xl text-primary-foreground/90 text-pretty">
            Subscribe to our newsletter and get the latest insights delivered directly to your inbox.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
            <Input
              placeholder="Enter your email"
              className="bg-primary-foreground text-foreground placeholder:text-muted-foreground"
            />
            <Button variant="secondary" size="lg">
              Subscribe
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
