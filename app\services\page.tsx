import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import {
  Code,
  Smartphone,
  TrendingUp,
  <PERSON>lette,
  ShoppingCart,
  Users,
  ArrowRight,
  CheckCircle,
  Clock,
  DollarSign,
  Zap,
} from "lucide-react"

export default function ServicesPage() {
  const services = [
    {
      icon: Code,
      title: "Web Development",
      description: "Custom websites and web applications built with cutting-edge technologies.",
      longDescription:
        "We create responsive, fast-loading websites and web applications using modern frameworks like React, Next.js, and Node.js. Our development process ensures scalability, security, and optimal performance.",
      features: [
        "Responsive Design",
        "SEO Optimization",
        "Performance Optimization",
        "Security Implementation",
        "CMS Integration",
        "API Development",
      ],
      process: ["Discovery & Planning", "Design & Prototyping", "Development", "Testing & QA", "Launch & Support"],
      pricing: "Starting from $2,500",
      timeline: "4-8 weeks",
      category: "Development",
    },
    {
      icon: Smartphone,
      title: "Mobile App Development",
      description: "Native and cross-platform mobile applications for iOS and Android.",
      longDescription:
        "We develop high-performance mobile applications using React Native, Flutter, and native technologies. Our apps are designed for optimal user experience and app store success.",
      features: [
        "Cross-Platform Development",
        "Native Performance",
        "App Store Optimization",
        "Push Notifications",
        "Offline Functionality",
        "Analytics Integration",
      ],
      process: ["Concept & Strategy", "UI/UX Design", "Development", "Testing", "App Store Submission"],
      pricing: "Starting from $5,000",
      timeline: "8-12 weeks",
      category: "Development",
    },
    {
      icon: TrendingUp,
      title: "Digital Marketing",
      description: "Comprehensive digital marketing strategies to grow your online presence.",
      longDescription:
        "Our digital marketing services include SEO, PPC advertising, social media marketing, content marketing, and analytics. We help businesses reach their target audience and achieve measurable growth.",
      features: [
        "Search Engine Optimization",
        "Pay-Per-Click Advertising",
        "Social Media Marketing",
        "Content Marketing",
        "Email Marketing",
        "Analytics & Reporting",
      ],
      process: ["Market Research", "Strategy Development", "Campaign Setup", "Optimization", "Reporting"],
      pricing: "Starting from $1,500/month",
      timeline: "Ongoing",
      category: "Marketing",
    },
    {
      icon: Palette,
      title: "UI/UX Design",
      description: "Beautiful and intuitive user interfaces that provide exceptional experiences.",
      longDescription:
        "We create user-centered designs that are both beautiful and functional. Our design process includes user research, wireframing, prototyping, and usability testing to ensure optimal user experience.",
      features: ["User Research", "Wireframing", "Prototyping", "Visual Design", "Usability Testing", "Design Systems"],
      process: ["Research", "Wireframing", "Visual Design", "Prototyping", "Testing & Iteration"],
      pricing: "Starting from $2,000",
      timeline: "3-6 weeks",
      category: "Design",
    },
    {
      icon: ShoppingCart,
      title: "E-commerce Solutions",
      description: "Complete e-commerce platforms with payment integration and management systems.",
      longDescription:
        "We build comprehensive e-commerce solutions using platforms like Shopify, WooCommerce, and custom solutions. Our e-commerce sites are optimized for conversions and include all necessary features for online selling.",
      features: [
        "Payment Gateway Integration",
        "Inventory Management",
        "Order Management",
        "Customer Accounts",
        "Analytics Dashboard",
        "Mobile Optimization",
      ],
      process: ["Requirements Analysis", "Platform Selection", "Development", "Payment Setup", "Launch & Training"],
      pricing: "Starting from $3,500",
      timeline: "6-10 weeks",
      category: "Development",
    },
    {
      icon: Users,
      title: "Consulting Services",
      description: "Strategic technology consulting to help your business make informed decisions.",
      longDescription:
        "Our consulting services help businesses navigate digital transformation, choose the right technologies, and optimize their processes. We provide strategic guidance and technical expertise.",
      features: [
        "Technology Strategy",
        "Digital Transformation",
        "Process Optimization",
        "Technical Audits",
        "Team Training",
        "Project Management",
      ],
      process: ["Assessment", "Strategy Development", "Implementation Planning", "Execution Support", "Review"],
      pricing: "Starting from $150/hour",
      timeline: "Varies",
      category: "Consulting",
    },
  ]

  const categories = ["All", "Development", "Design", "Marketing", "Consulting"]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-balance">Our Services</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto text-pretty">
            Comprehensive digital solutions designed to transform your business and drive growth. From web development
            to digital marketing, we've got you covered.
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {services.map((service, index) => (
              <Card key={index} className="group hover:shadow-xl transition-all duration-300 border-2">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                        <service.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-2xl">{service.title}</CardTitle>
                        <Badge variant="secondary" className="mt-2">
                          {service.category}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <CardDescription className="text-base mt-4">{service.longDescription}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Features */}
                  <div>
                    <h4 className="font-semibold mb-3">Key Features</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                          <span className="text-sm text-muted-foreground">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Process */}
                  <div>
                    <h4 className="font-semibold mb-3">Our Process</h4>
                    <div className="flex flex-wrap gap-2">
                      {service.process.map((step, stepIndex) => (
                        <Badge key={stepIndex} variant="outline" className="text-xs">
                          {stepIndex + 1}. {step}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Pricing & Timeline */}
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <DollarSign className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">{service.pricing}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-primary" />
                        <span className="text-sm font-medium">{service.timeline}</span>
                      </div>
                    </div>
                    <Button asChild size="sm">
                      <Link href="/contact">
                        Get Quote
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Our Services */}
      <section className="py-20 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-balance">Why Choose Our Services?</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
              We deliver exceptional results through our proven methodology and expert team.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                <Zap className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold">Expert Team</h3>
              <p className="text-muted-foreground">
                Our team consists of experienced professionals with proven track records in their respective fields.
              </p>
            </div>
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold">Quality Assurance</h3>
              <p className="text-muted-foreground">
                We follow rigorous quality assurance processes to ensure every project meets the highest standards.
              </p>
            </div>
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                <Users className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold">Ongoing Support</h3>
              <p className="text-muted-foreground">
                We provide continuous support and maintenance to ensure your project's long-term success.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center space-y-8">
          <h2 className="text-3xl md:text-4xl font-bold text-balance">Ready to Get Started?</h2>
          <p className="text-xl text-primary-foreground/90 text-pretty">
            Let's discuss your project requirements and create a custom solution that fits your needs and budget.
          </p>
          <Button asChild size="lg" variant="secondary" className="text-lg px-8 py-6">
            <Link href="/contact">
              Request a Quote
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
