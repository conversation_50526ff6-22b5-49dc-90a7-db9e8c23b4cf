"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"
import Image from "next/image"
import { useEffect, useState } from "react"
import { UserLayout } from "@/src/layouts/user-layout"
import {
  Code,
  Smartphone,
  TrendingUp,
  Palette,
  ShoppingCart,
  Users,
  ArrowRight,
  Star,
  CheckCircle,
  Zap,
  Shield,
  Award,
} from "lucide-react"

export default function HomePage() {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  const services = [
    {
      icon: Code,
      title: "Web Development",
      description: "Custom websites and web applications built with modern technologies and best practices.",
      features: ["Responsive Design", "SEO Optimized", "Fast Loading"],
      image: "/web-development-service.png",
    },
    {
      icon: Smartphone,
      title: "Mobile App Development",
      description: "Native and cross-platform mobile applications for iOS and Android devices.",
      features: ["Native Performance", "Cross-Platform", "App Store Ready"],
      image: "/mobile-app-development.png",
    },
    {
      icon: TrendingUp,
      title: "Digital Marketing",
      description: "Comprehensive digital marketing strategies to grow your online presence and reach.",
      features: ["SEO & SEM", "Social Media", "Analytics"],
      image: "/digital-marketing-service.png",
    },
    {
      icon: Palette,
      title: "UI/UX Design",
      description: "Beautiful and intuitive user interfaces that provide exceptional user experiences.",
      features: ["User Research", "Prototyping", "Design Systems"],
      image: "/ui-ux-design-service.png",
    },
    {
      icon: ShoppingCart,
      title: "E-commerce Solutions",
      description: "Complete e-commerce platforms with payment integration and inventory management.",
      features: ["Payment Gateway", "Inventory System", "Order Management"],
      image: "/ecommerce-solutions.png",
    },
    {
      icon: Users,
      title: "Consulting Services",
      description: "Strategic technology consulting to help your business make informed decisions.",
      features: ["Technology Strategy", "Digital Transformation", "Process Optimization"],
      image: "/consulting-services.png",
    },
  ]

  const stats = [
    { number: "500+", label: "Projects Completed" },
    { number: "200+", label: "Happy Clients" },
    { number: "50+", label: "Team Members" },
    { number: "5+", label: "Years Experience" },
  ]

  const testimonials = [
    {
      name: "Sarah Johnson",
      company: "TechStart Inc.",
      content: "DigiWave transformed our digital presence completely. Their team delivered beyond our expectations.",
      rating: 5,
      avatar: "/client-sarah-johnson.png",
    },
    {
      name: "Michael Chen",
      company: "Global Retail Co.",
      content: "The e-commerce solution they built increased our online sales by 300%. Highly recommended!",
      rating: 5,
      avatar: "/client-michael-chen.png",
    },
    {
      name: "Emily Rodriguez",
      company: "Creative Agency",
      content: "Professional, reliable, and innovative. DigiWave is our go-to partner for all digital projects.",
      rating: 5,
      avatar: "/client-emily-rodriguez.png",
    },
  ]

  return (
    <UserLayout>
      <div className="flex flex-col">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20 lg:py-32 overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image
              src="/hero-digital-transformation.png"
              alt="Digital transformation background"
              fill
              className="object-cover opacity-10"
              priority
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-background/80 to-background/60 z-10" />

          <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div
              className={`text-center space-y-8 transition-all duration-1000 ${isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"}`}
            >
              <div className="space-y-4">
                <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-balance animate-fade-in">
                  Transform Your
                  <span className="text-primary animate-pulse"> Digital </span>
                  Presence
                </h1>
                <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto text-pretty animate-slide-up">
                  We create innovative digital solutions that drive growth, enhance user experiences, and deliver
                  measurable results for your business.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up-delayed">
                <Button
                  asChild
                  size="lg"
                  className="bg-primary hover:bg-primary/90 text-lg px-8 py-6 hover:scale-105 transition-transform duration-200"
                >
                  <Link href="/contact">
                    Get Started Today
                    <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="text-lg px-8 py-6 bg-transparent hover:scale-105 transition-transform duration-200"
                >
                  <Link href="/portfolio">View Our Work</Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-primary text-primary-foreground">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center group hover:scale-110 transition-transform duration-300">
                  <div
                    className="text-3xl md:text-4xl font-bold text-secondary animate-bounce-in"
                    style={{ animationDelay: `${index * 0.2}s` }}
                  >
                    {stat.number}
                  </div>
                  <div className="text-primary-foreground/80 mt-2">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-balance">Our Services</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
                Comprehensive digital solutions tailored to meet your business needs and drive success.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {services.map((service, index) => (
                <Card
                  key={index}
                  className="group hover:shadow-xl transition-all duration-500 border-2 hover:border-primary/20 hover:-translate-y-2 overflow-hidden"
                >
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={service.image || "/placeholder.svg"}
                      alt={service.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
                    <div className="absolute bottom-4 left-4">
                      <div className="w-12 h-12 bg-primary/90 rounded-lg flex items-center justify-center group-hover:bg-primary transition-colors">
                        <service.icon className="h-6 w-6 text-white" />
                      </div>
                    </div>
                  </div>
                  <CardHeader>
                    <CardTitle className="text-xl group-hover:text-primary transition-colors">
                      {service.title}
                    </CardTitle>
                    <CardDescription className="text-base">{service.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {service.features.map((feature, featureIndex) => (
                        <li
                          key={featureIndex}
                          className="flex items-center space-x-2 group-hover:translate-x-1 transition-transform duration-300"
                          style={{ transitionDelay: `${featureIndex * 0.1}s` }}
                        >
                          <CheckCircle className="h-4 w-4 text-primary" />
                          <span className="text-sm text-muted-foreground">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-20 bg-muted/30 relative overflow-hidden">
          <div className="absolute inset-0 opacity-5">
            <Image src="/abstract-tech-pattern.png" alt="Tech pattern background" fill className="object-cover" />
          </div>
          <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-balance">Why Choose DigiWave?</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
                We combine expertise, innovation, and dedication to deliver exceptional results.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center space-y-4 group hover:scale-105 transition-transform duration-300">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto group-hover:bg-primary/20 transition-colors group-hover:rotate-12 duration-300">
                  <Zap className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Fast Delivery</h3>
                <p className="text-muted-foreground">
                  We deliver projects on time without compromising on quality, ensuring your business stays ahead.
                </p>
              </div>
              <div className="text-center space-y-4 group hover:scale-105 transition-transform duration-300">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto group-hover:bg-primary/20 transition-colors group-hover:rotate-12 duration-300">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Secure & Reliable</h3>
                <p className="text-muted-foreground">
                  Security and reliability are at the core of everything we build, protecting your business and users.
                </p>
              </div>
              <div className="text-center space-y-4 group hover:scale-105 transition-transform duration-300">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto group-hover:bg-primary/20 transition-colors group-hover:rotate-12 duration-300">
                  <Award className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">Award-Winning</h3>
                <p className="text-muted-foreground">
                  Our work has been recognized by industry leaders and has won multiple awards for excellence.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-4 mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-balance">What Our Clients Say</h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
                Don't just take our word for it. Here's what our satisfied clients have to say.
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {testimonials.map((testimonial, index) => (
                <Card
                  key={index}
                  className="text-center hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <CardContent className="pt-6">
                    <div className="relative w-16 h-16 mx-auto mb-4">
                      <Image
                        src={testimonial.avatar || "/placeholder.svg"}
                        alt={testimonial.name}
                        fill
                        className="object-cover rounded-full border-2 border-primary/20"
                      />
                    </div>
                    <div className="flex justify-center mb-4">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star
                          key={i}
                          className="h-5 w-5 text-secondary fill-current animate-pulse"
                          style={{ animationDelay: `${i * 0.1}s` }}
                        />
                      ))}
                    </div>
                    <p className="text-muted-foreground mb-4 italic">"{testimonial.content}"</p>
                    <div>
                      <p className="font-semibold">{testimonial.name}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.company}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-primary text-primary-foreground relative overflow-hidden">
          <div className="absolute inset-0 z-0">
            <Image src="/cta-background-pattern.png" alt="CTA background" fill className="object-cover opacity-10" />
          </div>
          <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold text-balance">Ready to Transform Your Business?</h2>
            <p className="text-xl text-primary-foreground/90 text-pretty">
              Let's discuss your project and create something amazing together. Get in touch with our team today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                asChild
                size="lg"
                variant="secondary"
                className="text-lg px-8 py-6 hover:scale-105 transition-transform duration-200"
              >
                <Link href="/contact">
                  Start Your Project
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="text-lg px-8 py-6 border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary bg-transparent hover:scale-105 transition-all duration-200"
              >
                <Link href="/portfolio">View Portfolio</Link>
              </Button>
            </div>
          </div>
        </section>
      </div>
    </UserLayout>
  )
}
