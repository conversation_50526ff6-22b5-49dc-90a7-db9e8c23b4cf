import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { Target, Heart, Lightbulb, Shield, Zap, Globe, Calendar, ArrowRight } from "lucide-react"

export default function AboutPage() {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      image: "/professional-ceo-headshot.png",
      bio: "10+ years of experience in digital transformation and business strategy.",
      expertise: ["Business Strategy", "Digital Transformation", "Leadership"],
    },
    {
      name: "<PERSON>",
      role: "CTO",
      image: "/professional-cto-headshot.png",
      bio: "Expert in full-stack development and system architecture with 8+ years experience.",
      expertise: ["Full-Stack Development", "System Architecture", "DevOps"],
    },
    {
      name: "<PERSON>",
      role: "Head of Design",
      image: "/professional-designer-headshot.png",
      bio: "Award-winning designer specializing in user experience and interface design.",
      expertise: ["UI/UX Design", "Brand Identity", "User Research"],
    },
    {
      name: "<PERSON>",
      role: "Marketing Director",
      image: "/professional-man-marketing.png",
      bio: "Digital marketing expert with proven track record in growth and lead generation.",
      expertise: ["Digital Marketing", "SEO/SEM", "Content Strategy"],
    },
    {
      name: "David Kim",
      role: "Lead Developer",
      image: "/professional-developer-headshot.png",
      bio: "Senior developer with expertise in modern web technologies and mobile development.",
      expertise: ["React/Next.js", "Mobile Development", "API Development"],
    },
    {
      name: "Lisa Thompson",
      role: "Project Manager",
      image: "/professional-project-manager-headshot.png",
      bio: "Certified project manager ensuring smooth delivery and client satisfaction.",
      expertise: ["Project Management", "Agile/Scrum", "Client Relations"],
    },
  ]

  const values = [
    {
      icon: Target,
      title: "Excellence",
      description: "We strive for excellence in every project, delivering solutions that exceed expectations.",
    },
    {
      icon: Heart,
      title: "Client-Focused",
      description: "Our clients' success is our success. We build lasting partnerships based on trust and results.",
    },
    {
      icon: Lightbulb,
      title: "Innovation",
      description: "We embrace cutting-edge technologies and creative solutions to solve complex challenges.",
    },
    {
      icon: Shield,
      title: "Reliability",
      description: "Dependable delivery, secure solutions, and consistent support you can count on.",
    },
    {
      icon: Zap,
      title: "Agility",
      description: "We adapt quickly to changing requirements and market conditions to keep you ahead.",
    },
    {
      icon: Globe,
      title: "Global Perspective",
      description: "We bring international best practices and global insights to every local project.",
    },
  ]

  const milestones = [
    {
      year: "2019",
      title: "Company Founded",
      description: "DigiWave was established with a vision to transform businesses through technology.",
    },
    {
      year: "2020",
      title: "First 50 Clients",
      description: "Reached our first major milestone of serving 50 satisfied clients.",
    },
    {
      year: "2021",
      title: "Team Expansion",
      description: "Grew our team to 25+ professionals across multiple disciplines.",
    },
    {
      year: "2022",
      title: "Award Recognition",
      description: "Won 'Best Digital Agency' award from Tech Innovation Council.",
    },
    {
      year: "2023",
      title: "200+ Projects",
      description: "Successfully completed over 200 projects across various industries.",
    },
    {
      year: "2024",
      title: "Global Reach",
      description: "Expanded services to serve clients across 15+ countries worldwide.",
    },
  ]

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-balance">About DigiWave</h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto text-pretty">
            We're a passionate team of digital experts dedicated to transforming businesses through innovative
            technology solutions and exceptional service.
          </p>
        </div>
      </section>

      {/* Company Story */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl font-bold text-balance">Our Story</h2>
              <div className="space-y-4 text-muted-foreground leading-relaxed">
                <p>
                  Founded in 2019, DigiWave emerged from a simple yet powerful vision: to bridge the gap between
                  traditional businesses and the digital future. Our founders, with decades of combined experience in
                  technology and business strategy, recognized that many companies were struggling to adapt to the
                  rapidly evolving digital landscape.
                </p>
                <p>
                  What started as a small team of passionate developers and designers has grown into a full-service
                  digital agency serving clients worldwide. We've helped over 200 businesses transform their operations,
                  reach new customers, and achieve sustainable growth through technology.
                </p>
                <p>
                  Today, DigiWave stands as a trusted partner for businesses of all sizes, from startups to enterprise
                  corporations, providing comprehensive digital solutions that drive real results.
                </p>
              </div>
              <Button asChild size="lg">
                <Link href="/contact">
                  Work With Us
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
            <div className="relative">
              <img
                src="/modern-office-collaboration.png"
                alt="DigiWave team collaboration"
                className="rounded-lg shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-balance">Our Values</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
              The principles that guide everything we do and shape our relationships with clients and team members.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <value.icon className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-balance">Meet Our Team</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
              The talented professionals behind DigiWave's success, bringing diverse expertise and passion to every
              project.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <img
                    src={member.image || "/placeholder.svg"}
                    alt={member.name}
                    className="w-32 h-32 rounded-full mx-auto mb-4 object-cover"
                  />
                  <CardTitle className="text-xl">{member.name}</CardTitle>
                  <CardDescription className="text-primary font-medium">{member.role}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground text-sm">{member.bio}</p>
                  <div className="flex flex-wrap gap-2 justify-center">
                    {member.expertise.map((skill, skillIndex) => (
                      <Badge key={skillIndex} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-20 bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-balance">Our Journey</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto text-pretty">
              Key milestones that have shaped DigiWave into the company we are today.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {milestones.map((milestone, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold">
                      <Calendar className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{milestone.title}</CardTitle>
                      <Badge variant="outline">{milestone.year}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{milestone.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center space-y-8">
          <h2 className="text-3xl md:text-4xl font-bold text-balance">Ready to Work Together?</h2>
          <p className="text-xl text-primary-foreground/90 text-pretty">
            Join the 200+ businesses that have transformed their digital presence with DigiWave. Let's create something
            amazing together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" variant="secondary" className="text-lg px-8 py-6">
              <Link href="/contact">Get In Touch</Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="lg"
              className="text-lg px-8 py-6 border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary bg-transparent"
            >
              <Link href="/portfolio">View Our Work</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
