@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated to Royal theme colors */
  --background: oklch(1 0 0); /* White */
  --foreground: oklch(0.2 0 0); /* Black */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.2 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2 0 0);
  --primary: oklch(0.55 0.15 258); /* Royal Blue #4169E1 */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.85 0.12 85); /* Gold #FFD700 */
  --secondary-foreground: oklch(0.2 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.85 0.12 85); /* Gold accent */
  --accent-foreground: oklch(0.2 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.922 0 0);
  --input: oklch(0.97 0 0);
  --ring: oklch(0.55 0.15 258);
  --chart-1: oklch(0.55 0.15 258);
  --chart-2: oklch(0.85 0.12 85);
  --chart-3: oklch(0.2 0 0);
  --chart-4: oklch(0.7 0.1 258);
  --chart-5: oklch(0.9 0.08 85);
  --radius: 0.625rem;
  --sidebar: oklch(0.98 0 0);
  --sidebar-foreground: oklch(0.2 0 0);
  --sidebar-primary: oklch(0.55 0.15 258);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.2 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.55 0.15 258);
}

.dark {
  /* Dark mode Royal theme */
  --background: oklch(0.15 0 0); /* Dark background */
  --foreground: oklch(0.95 0 0); /* Light text */
  --card: oklch(0.18 0 0);
  --card-foreground: oklch(0.95 0 0);
  --popover: oklch(0.18 0 0);
  --popover-foreground: oklch(0.95 0 0);
  --primary: oklch(0.6 0.18 258); /* Brighter Royal Blue for dark mode */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.8 0.15 85); /* Adjusted Gold for dark mode */
  --secondary-foreground: oklch(0.15 0 0);
  --muted: oklch(0.25 0 0);
  --muted-foreground: oklch(0.7 0 0);
  --accent: oklch(0.8 0.15 85);
  --accent-foreground: oklch(0.15 0 0);
  --destructive: oklch(0.6 0.2 25);
  --destructive-foreground: oklch(0.95 0 0);
  --border: oklch(0.25 0 0);
  --input: oklch(0.25 0 0);
  --ring: oklch(0.6 0.18 258);
  --chart-1: oklch(0.6 0.18 258);
  --chart-2: oklch(0.8 0.15 85);
  --chart-3: oklch(0.95 0 0);
  --chart-4: oklch(0.7 0.15 258);
  --chart-5: oklch(0.85 0.12 85);
  --sidebar: oklch(0.2 0 0);
  --sidebar-foreground: oklch(0.95 0 0);
  --sidebar-primary: oklch(0.6 0.18 258);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.25 0 0);
  --sidebar-accent-foreground: oklch(0.95 0 0);
  --sidebar-border: oklch(0.25 0 0);
  --sidebar-ring: oklch(0.6 0.18 258);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Added custom animations for enhanced user experience */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 1s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out 0.2s both;
  }

  .animate-slide-up-delayed {
    animation: slideUp 0.8s ease-out 0.4s both;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out both;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(65, 105, 225, 0.3);
  }
  to {
    box-shadow: 0 0 30px rgba(65, 105, 225, 0.6);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary) / 80;
}
