import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { ArrowLeft, Calendar, Clock, Share2, Tag, ArrowRight } from "lucide-react"

export default function BlogPostPage({ params }: { params: { id: string } }) {
  // <PERSON>ck blog post data - in a real app, this would be fetched based on the ID
  const post = {
    id: 1,
    title: "The Future of Web Development: Trends to Watch in 2024",
    content: `
      <p>The web development landscape is constantly evolving, and 2024 promises to bring exciting new trends and technologies that will shape how we build and interact with web applications. As we move forward, developers and businesses alike need to stay informed about these emerging trends to remain competitive and deliver exceptional user experiences.</p>

      <h2>1. AI-Powered Development Tools</h2>
      <p>Artificial Intelligence is revolutionizing the way we write code. From GitHub Copilot to ChatGPT, AI-powered tools are becoming indispensable for developers, helping with code generation, debugging, and even architectural decisions. These tools are not replacing developers but rather augmenting their capabilities, allowing them to focus on higher-level problem-solving and creative solutions.</p>

      <h2>2. Progressive Web Apps (PWAs) Go Mainstream</h2>
      <p>Progressive Web Apps continue to bridge the gap between web and native applications. With improved browser support and enhanced capabilities, PWAs are becoming the go-to solution for businesses looking to provide app-like experiences without the complexity of native development. Features like offline functionality, push notifications, and device integration make PWAs increasingly attractive.</p>

      <h2>3. The Rise of Edge Computing</h2>
      <p>Edge computing is transforming how we think about web application architecture. By processing data closer to the user, edge computing reduces latency and improves performance. Platforms like Vercel Edge Functions and Cloudflare Workers are making it easier than ever to deploy code at the edge, enabling faster, more responsive applications.</p>

      <h2>4. WebAssembly (WASM) Adoption</h2>
      <p>WebAssembly is opening new possibilities for web applications by allowing developers to run high-performance code written in languages like Rust, C++, and Go directly in the browser. This technology is particularly valuable for computationally intensive applications like image processing, gaming, and scientific computing.</p>

      <h2>5. Micro-Frontends Architecture</h2>
      <p>As applications grow in complexity, micro-frontends are becoming a popular architectural pattern. This approach allows teams to develop, deploy, and maintain different parts of a web application independently, improving scalability and team autonomy while reducing the risk of monolithic application issues.</p>

      <h2>Conclusion</h2>
      <p>The future of web development is bright and full of opportunities. By staying informed about these trends and experimenting with new technologies, developers and businesses can create more efficient, performant, and user-friendly web applications. The key is to evaluate each trend's relevance to your specific use case and adopt those that provide real value to your projects and users.</p>
    `,
    image: "/web-development-trends-2024.png",
    author: "Sarah Chen",
    authorImage: "/professional-cto-headshot.png",
    authorBio: "Sarah is our CTO with over 8 years of experience in full-stack development and system architecture.",
    date: "2024-01-15",
    readTime: "8 min read",
    category: "Web Development",
    tags: ["JavaScript", "AI", "PWA", "Trends"],
  }

  const relatedPosts = [
    {
      id: 2,
      title: "Mobile-First Design: Why It Matters More Than Ever",
      image: "/mobile-first-design-strategy.png",
      category: "Design",
    },
    {
      id: 6,
      title: "Building High-Performance React Applications",
      image: "/react-performance-optimization.png",
      category: "Development",
    },
  ]

  return (
    <div className="flex flex-col">
      {/* Back Navigation */}
      <section className="py-6 border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Button variant="ghost" asChild>
            <Link href="/blog">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Blog
            </Link>
          </Button>
        </div>
      </section>

      {/* Article Header */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-6">
            <Badge className="bg-secondary text-secondary-foreground">{post.category}</Badge>
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-balance">{post.title}</h1>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-6 text-muted-foreground">
              <div className="flex items-center space-x-2">
                <img src={post.authorImage || "/placeholder.svg"} alt={post.author} className="w-8 h-8 rounded-full" />
                <span className="font-medium">{post.author}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4" />
                <span>{new Date(post.date).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span>{post.readTime}</span>
              </div>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2">
              {post.tags.map((tag, index) => (
                <Badge key={index} variant="outline">
                  <Tag className="h-3 w-3 mr-1" />
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Featured Image */}
      <section className="py-8">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <img
            src={post.image || "/placeholder.svg"}
            alt={post.title}
            className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
          />
        </div>
      </section>

      {/* Article Content */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div
            className="prose prose-lg max-w-none prose-headings:text-foreground prose-p:text-muted-foreground prose-strong:text-foreground"
            dangerouslySetInnerHTML={{ __html: post.content }}
          />
        </div>
      </section>

      {/* Author Bio */}
      <section className="py-12 bg-muted/30">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <Card>
            <CardContent className="flex items-start space-x-4 pt-6">
              <img src={post.authorImage || "/placeholder.svg"} alt={post.author} className="w-16 h-16 rounded-full" />
              <div className="flex-1">
                <h3 className="text-xl font-semibold mb-2">About {post.author}</h3>
                <p className="text-muted-foreground">{post.authorBio}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Related Posts */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold mb-8 text-center">Related Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {relatedPosts.map((relatedPost) => (
              <Card key={relatedPost.id} className="group hover:shadow-lg transition-shadow">
                <div className="relative overflow-hidden">
                  <img
                    src={relatedPost.image || "/placeholder.svg"}
                    alt={relatedPost.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <Badge className="absolute top-4 left-4 bg-secondary text-secondary-foreground">
                    {relatedPost.category}
                  </Badge>
                </div>
                <CardHeader>
                  <CardTitle className="group-hover:text-primary transition-colors">
                    <Link href={`/blog/${relatedPost.id}`}>{relatedPost.title}</Link>
                  </CardTitle>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center space-y-8">
          <h2 className="text-3xl md:text-4xl font-bold text-balance">Ready to Transform Your Business?</h2>
          <p className="text-xl text-primary-foreground/90 text-pretty">
            Let's discuss how we can help you implement these cutting-edge technologies in your next project.
          </p>
          <Button asChild size="lg" variant="secondary" className="text-lg px-8 py-6">
            <Link href="/contact">
              Get In Touch
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
